<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas Preview</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background: #f5f5f5;
            color: #666;
            font-size: 18px;
        }

        .error {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background: #f5f5f5;
            color: #d32f2f;
            font-size: 18px;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div id="loading" class="loading">Loading canvas content...</div>
    <div id="error" class="error" style="display: none;">
        <div>
            <h2>Canvas Error</h2>
            <p>No HTML content found in URL parameters.</p>
            <p>This page is designed to display AI-generated HTML content.</p>
        </div>
    </div>

    <script>
        function loadCanvasContent() {
            try {
                // Get the HTML content from URL parameters
                const urlParams = new URLSearchParams(window.location.search);
                const encodedHtml = urlParams.get('html');

                if (!encodedHtml) {
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('error').style.display = 'flex';
                    return;
                }

                // Decode the HTML content
                const htmlContent = decodeURIComponent(encodedHtml);

                // Replace the entire document with the decoded HTML
                document.open();
                document.write(htmlContent);
                document.close();

            } catch (error) {
                console.error('Error loading canvas content:', error);
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'flex';
                document.getElementById('error').innerHTML = `
                    <div>
                        <h2>Canvas Error</h2>
                        <p>Failed to load HTML content: ${error.message}</p>
                        <p>Please check the console for more details.</p>
                    </div>
                `;
            }
        }

        // Load content when page is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', loadCanvasContent);
        } else {
            loadCanvasContent();
        }
    </script>
</body>
</html>