<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A Little Bit of Sunshine!</title>
    <style>
        body {
            font-family: 'Comic Sans MS', cursive, sans-serif; /* A cute font! */
            background-color: #ffe0f0; /* Soft pink background */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            overflow: hidden; /* Hide any overflow from animations */
            position: relative;
        }

        .container {
            text-align: center;
            background-color: #fff;
            padding: 40px 50px;
            border-radius: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 10;
            animation: bounceIn 1s ease-out;
        }

        h1 {
            color: #ff69b4; /* Hot pink */
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        p {
            color: #8a2be2; /* Blue violet */
            font-size: 1.2em;
            line-height: 1.6;
        }

        .heart {
            color: #ff4500; /* Orange red */
            font-size: 2.5em;
            animation: pulse 1.5s infinite alternate;
            display: inline-block;
            margin: 0 5px;
        }

        .button {
            display: inline-block;
            margin-top: 30px;
            padding: 12px 25px;
            background-color: #ffd700; /* Gold */
            color: #fff;
            text-decoration: none;
            border-radius: 30px;
            font-size: 1.1em;
            font-weight: bold;
            transition: transform 0.2s ease, background-color 0.2s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .button:hover {
            background-color: #ffa500; /* Orange */
            transform: translateY(-3px);
        }

        /* Cute background elements */
        .star {
            position: absolute;
            color: #fffacd; /* Lemon Chiffon */
            font-size: 1.5em;
            animation: twinkle 3s infinite ease-in-out alternate;
            opacity: 0.8;
            pointer-events: none; /* Make them not interfere with clicks */
        }

        .star:nth-child(1) { top: 10%; left: 15%; animation-delay: 0s; }
        .star:nth-child(2) { top: 25%; right: 20%; font-size: 1.2em; animation-delay: 0.5s; }
        .star:nth-child(3) { bottom: 15%; left: 30%; font-size: 1.8em; animation-delay: 1s; }
        .star:nth-child(4) { top: 40%; left: 5%; font-size: 1em; animation-delay: 1.5s; }
        .star:nth-child(5) { bottom: 5%; right: 10%; font-size: 2em; animation-delay: 2s; }

        /* Animations */
        @keyframes pulse {
            from { transform: scale(1); }
            to { transform: scale(1.1); }
        }

        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); opacity: 1; }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); }
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0.8; transform: scale(1); }
            50% { opacity: 0.3; transform: scale(1.2); }
        }

    </style>
</head>
<body>
    <div class="star">&#9733;</div>
    <div class="star">&#9733;</div>
    <div class="star">&#9733;</div>
    <div class="star">&#9733;</div>
    <div class="star">&#9733;</div>

    <div class="container">
        <h1>Hello, Sunshine!</h1>
        <p>You're absolutely wonderful <span class="heart">&hearts;</span> and deserve a bright day!</p>
        <p>Hope this little file brings a smile to your face. <span class="heart">&#9786;</span></p>
        <a href="#" class="button">Spread Happiness!</a>
    </div>
</body>
</html>